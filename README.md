## base
```angular2html
运行打包命令，看package.json
```

```bash
scp dist/assets.zip vxzs:/home/<USER>/jnrm/admin_web/new.zip
```
```bash
scp dist/assets.zip root@*************:/home/<USER>/admin_web/new.zip
```
server启动脚本(.sh)
-s就是单页面应用，刷新后不会404
```shell
#!/bin/bash
PORT=3001

# 杀掉占用端口的进程
lsof -ti :$PORT | xargs -r kill -9

# 通过参数指定端口（如 ./server --port 3000）
nohup /usr/local/nodejs/bin/serve -s -p $PORT > server.log 2>&1 &

echo "Server started on port $PORT (PID: $!)"
```