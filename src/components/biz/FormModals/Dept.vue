<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-2 gap-x-base">
					<XFormItem label="类型" prop="type">
						<XSelect v-model="model.type" placeholder="请选择电厂、煤厂" tag-group-name="ADMIN部门类型" />
					</XFormItem>
					<XFormItem label="名称" prop="name">
						<XInput v-model="model.name" placeholder="请输入名称" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="行政编码" prop="code">
						<XInput v-model="model.code" placeholder="请输入行政编码" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="联系人(电话)" prop="phone">
						<XInput v-model="model.phone" placeholder="请输入联系电话" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="邮箱" prop="email">
						<XInput v-model="model.email" placeholder="请输入邮箱" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<XFormItem label="位置" prop="locationId">
						<XSelect
							v-model:label="model.locationName"
							v-model="model.locationId"
							:remote-method="BIZ_JnrmLocation_APIS.getPage"
							:placeholder="model.locationName || '请选择位置'"
							@keyup.enter.stop="handleSubmit"
						/>
					</XFormItem>
					<!--					<XFormItem label="经度" prop="longitude">-->
					<!--						<XInput v-model="model.longitude" placeholder="请输入经度" @keyup.enter.stop="handleSubmit" />-->
					<!--					</XFormItem>-->
					<!--					<XFormItem label="纬度" prop="latitude">-->
					<!--						<XInput v-model="model.latitude" placeholder="请输入纬度" @keyup.enter.stop="handleSubmit" />-->
					<!--					</XFormItem>-->
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
const visible = ref(false)
const model = ref({
	id: undefined,
	name: undefined,
	parentId: undefined,
	sort: undefined,
	leaderUserId: undefined,
	phone: undefined,
	email: undefined,
	status: undefined,
	entityId: undefined,
	type: undefined,
	code: undefined,
	address: undefined,
	contactPerson: undefined,
	longitude: undefined,
	latitude: undefined,
})
const rules = reactive({
	type: [{ required: true, message: '类型不能为空' }],
	name: [{ required: true, message: '名称不能为空' }],
	parentId: [{ required: true, message: '父id不能为空' }],
	sort: [{ required: true, message: '显示顺序不能为空' }],
	status: [{ required: true, message: '状态（0正常 1停用）不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run, data
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
function open(_type, _id, _params) {
	model.value = { status: 0, sort: 100, parentId: 2 }
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	let { run: _run, loading, data: _data } = xUseRequest(BIZ_Dept_APIS[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id) {
	detailLoading.value = true
	const res = await BIZ_Dept_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped></style>
