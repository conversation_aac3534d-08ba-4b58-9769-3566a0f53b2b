<template>
	<BizModalsSave v-model="visible" :title="title">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-1 gap-x-base">
					<div class="grid grid-cols-2 gap-x-base">
						<XFormItem label="地点名称" prop="name">
							<XInput v-model="model.name" placeholder="请输入地点名称" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
						<XFormItem label="类型" prop="factoryType">
							<XSelect v-model="model.factoryType" placeholder="请选择类型" tag-group-name="JNRM部门类型" />
						</XFormItem>
					</div>
					<XFormItem label="详细地址" prop="address">
						<XInput v-model="model.address" placeholder="请输入详细地址" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<div class="grid grid-cols-2 gap-x-base">
						<XFormItem label="经度" prop="longitude">
							<XInput v-model="model.longitude" placeholder="请输入经度" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
						<XFormItem label="纬度" prop="latitude">
							<XInput v-model="model.latitude" placeholder="请输入纬度" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
					</div>
					<XFormItem label="电子围栏" prop="address1">
						<div class="map-container">
							<div id="baiduMap" class="baidu-map"></div>
							<div class="map-controls">
								<button type="button" @click="centerMap" class="center-btn">中心定位</button>
								<button type="button" @click="startDrawing" :disabled="isDrawing" class="draw-btn">
									{{ isDrawing ? '绘制中...' : '开始绘制多边形' }}
								</button>
								<button type="button" @click="clearDrawing" class="clear-btn">清除</button>
							</div>
						</div>
					</XFormItem>
					<div v-if="model.scope" class="scope-info">
						<div class="scope-header">围栏坐标：</div>
						<div class="scope-content">
							<div v-if="parsedScope.length > 0" class="coordinate-list">
								<div v-for="(point, index) in parsedScope" :key="index" class="coordinate-item">
									<span class="point-index">点{{ index + 1 }}:</span>
									<span class="coordinate-value">经度: {{ point.lng.toFixed(6) }}, 纬度: {{ point.lat.toFixed(6) }}</span>
								</div>
							</div>
							<div v-else class="coordinate-raw">
								<pre>{{ model.scope }}</pre>
							</div>
						</div>
					</div>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
// 声明全局变量类型
declare global {
	interface Window {
		BMapGL: any
		BMapGLLib: any
		BMAP_DRAWING_POLYGON: any
	}
}

const visible = ref(false)
const isDrawing = ref(false)
let map: any = null
let drawingManager: any = null
let currentPolygon: any = null

interface LocationModel {
	id?: number
	name?: string
	address?: string
	longitude?: string
	latitude?: string
	type?: string
	factoryType?: string
	scope?: string
}

const model = ref<LocationModel>({
	id: undefined,
	name: '',
	address: '',
	longitude: '',
	latitude: '',
	type: '',
	factoryType: '',
	scope: '',
})

const rules = reactive({
	name: [{ required: true, message: '地点名称不能为空' }],
	factoryType: [{ required: true, message: '类型不能为空' }],
})

const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run: any
let data: any
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})

/**
 * 解析围栏坐标JSON数据
 */
const parsedScope = computed(() => {
	if (!model.value.scope) return []
	try {
		const coordinates = JSON.parse(model.value.scope)
		return Array.isArray(coordinates) ? coordinates : []
	} catch (e) {
		console.error('解析围栏坐标失败:', e)
		return []
	}
})

function open(_type: string, _id: number, _params: any) {
	model.value = {
		id: undefined,
		name: undefined,
		address: undefined,
		longitude: undefined,
		latitude: undefined,
		type: undefined,
		factoryType: undefined,
		scope: undefined,
	}
	params.value = _params
	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	const { run: _run, loading, data: _data } = xUseRequest((BIZ_JnrmLocation_APIS as any)[_type], model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

async function getDetail(id: number) {
	detailLoading.value = true
	const res = await BIZ_JnrmLocation_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)
}

/**
 * 获取地图中心点坐标
 */
function getMapCenter() {
	const lng = parseFloat(model.value.longitude || '116.404') || 116.404
	const lat = parseFloat(model.value.latitude || '39.915') || 39.915
	return new window.BMapGL.Point(lng, lat)
}

/**
 * 中心定位功能
 */
function centerMap() {
	if (map) {
		const point = getMapCenter()
		map.centerAndZoom(point, 16)
	}
}

/**
 * 初始化百度地图
 */
function initBaiduMap() {
	if (typeof window.BMapGL === 'undefined') {
		console.error('百度地图API未加载')
		return
	}

	// 创建地图实例
	map = new window.BMapGL.Map('baiduMap', { enableMapClick: false })
	// 初始化地图，设置中心点坐标和地图级别，优先使用表单中的经纬度
	const point = getMapCenter()
	map.centerAndZoom(point, 11)
	// 开启鼠标滚轮缩放
	map.enableScrollWheelZoom(true)

	// 样式配置
	const styleOptions = {
		strokeColor: '#5E87DB', // 边线颜色
		fillColor: '#5E87DB', // 填充颜色
		strokeWeight: 2, // 边线宽度
		strokeOpacity: 1, // 边线透明度
		fillOpacity: 0.2, // 填充透明度
	}

	// 实例化鼠标绘制工具
	drawingManager = new window.BMapGLLib.DrawingManager(map, {
		enableCalculate: false, // 绘制是否进行测距测面
		enableSorption: true, // 是否开启边界吸附功能
		sorptiondistance: 20, // 边界吸附距离
		polygonOptions: styleOptions, // 多边形的样式
	})

	// 监听绘制完成事件
	drawingManager.addEventListener('overlaycomplete', function (e: any) {
		if (e.drawingMode === window.BMAP_DRAWING_POLYGON) {
			// 清除之前的多边形
			if (currentPolygon) {
				map.removeOverlay(currentPolygon)
			}

			currentPolygon = e.overlay

			// 获取多边形的坐标点
			const path = currentPolygon.getPath()
			const coordinates = []

			for (let i = 0; i < path.length; i++) {
				coordinates.push({
					lng: path[i].lng,
					lat: path[i].lat,
				})
			}

			// 将坐标保存为JSON字符串
			model.value.scope = JSON.stringify(coordinates)

			// 关闭绘制模式
			drawingManager.close()
			isDrawing.value = false
		}
	})
}

/**
 * 开始绘制多边形
 */
function startDrawing() {
	if (!drawingManager) {
		console.error('绘制管理器未初始化')
		return
	}

	isDrawing.value = true
	drawingManager.setDrawingMode(window.BMAP_DRAWING_POLYGON)
	drawingManager.open()
}

/**
 * 清除绘制的多边形
 */
function clearDrawing() {
	if (currentPolygon) {
		map.removeOverlay(currentPolygon)
		currentPolygon = null
	}
	model.value.scope = undefined
	isDrawing.value = false
	if (drawingManager) {
		drawingManager.close()
	}
}

/**
 * 根据已有的scope数据恢复多边形显示
 */
function restorePolygon() {
	if (model.value.scope && map) {
		try {
			const coordinates = JSON.parse(model.value.scope)
			if (Array.isArray(coordinates) && coordinates.length > 0) {
				// 清除之前的多边形
				if (currentPolygon) {
					map.removeOverlay(currentPolygon)
				}

				// 创建多边形点数组
				const points = coordinates.map((coord: any) => new window.BMapGL.Point(coord.lng, coord.lat))

				// 创建多边形
				currentPolygon = new window.BMapGL.Polygon(points, {
					strokeColor: '#5E87DB',
					fillColor: '#5E87DB',
					strokeWeight: 2,
					strokeOpacity: 1,
					fillOpacity: 0.2,
				})

				// 添加到地图
				map.addOverlay(currentPolygon)

				// 调整地图视野以包含多边形
				map.setViewport(points)
			}
		} catch (error) {
			console.error('解析围栏坐标失败:', error)
		}
	}
}

// 监听模态框显示状态，初始化地图
watch(visible, (newVal) => {
	if (newVal) {
		// 延迟初始化地图，确保DOM已渲染
		nextTick(() => {
			initBaiduMap()
			// 如果有已保存的围栏数据，恢复显示
			setTimeout(() => {
				restorePolygon()
			}, 500)
		})
	}
})

defineExpose({ open })
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value === 'create') {
		model.value.id = data
	}
	emit('success', model.value)
}
</script>

<style scoped>
.map-container {
	width: 100%;
	height: 400px;
	border: 1px solid #e1e1e1;
	border-radius: 4px;
	position: relative;
	overflow: hidden;
}

.baidu-map {
	width: 100%;
	height: 100%;
}

.map-controls {
	position: absolute;
	top: 10px;
	right: 10px;
	z-index: 1000;
	display: flex;
	gap: 8px;
}

.center-btn,
.draw-btn,
.clear-btn {
	padding: 6px 12px;
	border: none;
	border-radius: 4px;
	font-size: 12px;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.draw-btn {
	background-color: #409eff;
	color: white;
}

.center-btn {
	background-color: #409eff;
	color: white;
}

.draw-btn:hover:not(:disabled) {
	background-color: #66b1ff;
}

.draw-btn:disabled {
	background-color: #c0c4cc;
	cursor: not-allowed;
}

.clear-btn {
	background-color: #f56c6c;
	color: white;
}

.clear-btn:hover {
	background-color: #f78989;
}

.scope-info {
	margin-top: 10px;
	padding: 12px;
	background-color: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	font-size: 13px;
	color: #495057;
	max-height: 200px;
	overflow-y: auto;
}

.scope-header {
	font-weight: 600;
	color: #343a40;
	margin-bottom: 8px;
	font-size: 14px;
}

.scope-content {
	line-height: 1.5;
}

.coordinate-list {
	display: flex;
	flex-direction: column;
	gap: 6px;
}

.coordinate-item {
	display: flex;
	align-items: center;
	padding: 6px 8px;
	background-color: #ffffff;
	border: 1px solid #dee2e6;
	border-radius: 4px;
	transition: background-color 0.2s;
}

.coordinate-item:hover {
	background-color: #f8f9fa;
}

.point-index {
	font-weight: 500;
	color: #007bff;
	min-width: 50px;
	margin-right: 8px;
}

.coordinate-value {
	color: #6c757d;
	font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
	font-size: 12px;
}

.coordinate-raw {
	padding: 8px;
	background-color: #ffffff;
	border: 1px solid #dee2e6;
	border-radius: 4px;
}

.coordinate-raw pre {
	margin: 0;
	font-size: 11px;
	color: #6c757d;
	white-space: pre-wrap;
	word-break: break-all;
}
</style>
