<template>
	<BizModalsSave v-model="visible" :title="title" width="1200px">
		<div v-loading="computedLoading" class="pb-base">
			<BizFormForm :model="model" :rules="rules" @submit="handleSubmit">
				<div class="grid grid-cols-1 gap-y-base">
					<XFormItem label="路线名称" prop="routeName">
						<XInput v-model="model.routeName" placeholder="请输入路线名称" @keyup.enter.stop="handleSubmit" />
					</XFormItem>
					<!-- 基本信息 -->
					<div class="grid grid-cols-2 gap-x-base">
						<XFormItem label="出发地" prop="departureId">
							<XSelect
								v-model="model.departureId"
								v-model:label="model.departureName"
								:remote-method="() => BIZ_JnrmLocation_APIS.getPage({ pageSize: 20 })"
								:placeholder="model.departureName || '请选择出发地'"
								@change="handleDepartureChange"
								@keyup.enter.stop="handleSubmit"
							/>
						</XFormItem>
						<XFormItem label="目的地" prop="destinationId">
							<XSelect
								v-model="model.destinationId"
								v-model:label="model.destinationName"
								:remote-method="() => BIZ_JnrmLocation_APIS.getPage({ pageSize: 20 })"
								:placeholder="model.destinationName || '请选择目的地'"
								@change="handleDestinationChange"
								@keyup.enter.stop="handleSubmit"
							/>
						</XFormItem>
					</div>

					<!-- 路线推荐按钮 -->
					<div class="flex justify-center">
						<button type="button" :disabled="!canGetRecommendation || routeLoading" class="route-recommend-btn" @click="getRouteRecommendation">
							{{ routeLoading ? '获取路线中...' : '获取路线推荐' }}
						</button>
					</div>

					<!-- 地图和路线列表 -->
					<div v-if="recommendedRoutes.length > 0" class="grid grid-cols-3 gap-x-base">
						<!-- 路线列表 -->
						<div class="route-list">
							<h3 class="route-list-title">推荐路线</h3>
							<div class="route-items">
								<div
									v-for="(route, index) in recommendedRoutes"
									:key="index"
									:class="['route-item', { 'route-item-selected': selectedRouteIndex === index }]"
									@click="selectRoute(index)"
								>
									<div class="route-header">
										<span class="route-tag" :style="{ backgroundColor: routeColors[index] }">{{ route.tag }}</span>
										<span class="route-distance">{{ (route.distance / 1000).toFixed(1) }}km</span>
									</div>
									<div class="route-info">
										<div class="route-duration">预计时长: {{ Math.round(route.duration / 60) }}分钟</div>
										<div class="route-cost">油费: ¥{{ route.oil_cost.toFixed(2) }}</div>
										<div class="route-lights">红绿灯: {{ route.traffic_light_num }}个</div>
									</div>
									<button type="button" class="use-route-btn" @click.stop="useRoute(route, index)">使用此路线</button>
								</div>
							</div>
						</div>

						<!-- 地图显示 -->
						<div class="col-span-2">
							<div class="map-container">
								<div id="routeMap" class="route-map"></div>
							</div>
						</div>
					</div>

					<!-- 表单字段 -->
					<div class="grid grid-cols-2 gap-x-base">
						<XFormItem label="轨迹路径" prop="trackPath">
							<XInput v-model="model.trackPath" placeholder="请输入轨迹路径（JSON格式存储）" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
						<XFormItem label="路线距离（公里）" prop="distance">
							<XInput v-model="model.distance" placeholder="请输入路线距离（公里）" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
						<XFormItem label="预估时长（分钟）" prop="estimatedDuration">
							<XInput v-model="model.estimatedDuration" placeholder="请输入预估时长（分钟）" @keyup.enter.stop="handleSubmit" />
						</XFormItem>
					</div>
				</div>
			</BizFormForm>
		</div>
	</BizModalsSave>
</template>
<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, watchEffect } from 'vue'

// 声明全局变量类型
declare global {
	interface Window {
		BMapGL: any
		BMapGLLib: any
	}
}

// 路线表单组件，支持动态获取出发地和目的地的完整location信息

// 定义location接口类型
interface LocationInfo {
	id: number
	name: string
	longitude: string
	latitude: string
	address?: string
	type?: string
	factoryType?: string
}

// 定义路线接口类型
interface RouteStep {
	leg_index: number
	direction: number
	distance: number
	road_name: string
	road_type: number
	start_location: { lng: number; lat: number }
	end_location: { lng: number; lat: number }
	path: string
	traffic_condition: any[]
	restrictions: any[]
	duration: number
	adcodes: string
}

interface Route {
	origin: { lng: number; lat: number }
	destination: { lng: number; lat: number }
	tag: string
	distance: number
	duration: number
	steps: RouteStep[]
	oil_cost: number
	track_idx: number
	unavoid_polygons_idx: string
	traffic_light_num: number
}

interface RouteResponse {
	status: number
	message: string
	result: {
		restriction: { type: string; info: string }
		total: number
		routes: Route[]
		routesinfo_ext: string
	}
}

const visible = ref(false)
const model = ref({
	id: undefined as number | undefined,
	departureId: undefined as number | undefined,
	departureName: undefined as string | undefined,
	destinationId: undefined as number | undefined,
	destinationName: undefined as string | undefined,
	trackPath: undefined as string | undefined,
	distance: undefined as string | undefined,
	estimatedDuration: undefined as string | undefined,
	routeName: undefined as string | undefined,
})

// 存储完整的location信息
const departureLocation = ref<LocationInfo | null>(null)
const destinationLocation = ref<LocationInfo | null>(null)

// 路线推荐相关状态
const recommendedRoutes = ref<Route[]>([])
const selectedRouteIndex = ref<number>(-1)
const routeLoading = ref(false)
let map: any = null
let routePolylines: any[] = []

// 路线颜色配置
const routeColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']

// 计算是否可以获取路线推荐
const canGetRecommendation = computed(() => {
	return (
		departureLocation.value &&
		destinationLocation.value &&
		departureLocation.value.longitude &&
		departureLocation.value.latitude &&
		destinationLocation.value.longitude &&
		destinationLocation.value.latitude
	)
})
const rules = reactive({
	departureId: [{ required: true, message: '出发地ID不能为空' }],
	destinationId: [{ required: true, message: '目的地ID不能为空' }],
})
const emit = defineEmits(['success'])
const title = ref('')
let type = ref('')
const id = ref(0)
const detailLoading = ref(false)
const requestLoading = ref(false)
let run: any, data: any
const computedLoading = computed(() => requestLoading?.value || detailLoading.value)
const params = ref({})
/**
 * 打开表单弹窗
 * @param _type 操作类型：'create' 或 'update'
 * @param _id 路线ID（编辑时使用）
 * @param _params 额外参数
 */
function open(_type: string, _id: number, _params: any) {
	model.value = {
		id: undefined,
		departureId: undefined,
		departureName: undefined,
		destinationId: undefined,
		destinationName: undefined,
		trackPath: undefined,
		distance: undefined,
		estimatedDuration: undefined,
		routeName: undefined,
	}
	params.value = _params

	// 重置location信息
	departureLocation.value = null
	destinationLocation.value = null

	if (_id) {
		getDetail(_id)
	}
	visible.value = true
	type.value = _type
	title.value = _type === 'create' ? '新增' : '编辑'
	id.value = _id
	const apiMethod = _type === 'create' ? BIZ_JnrmRoute_APIS.create : BIZ_JnrmRoute_APIS.update
	let { run: _run, loading, data: _data } = xUseRequest(apiMethod, model)
	run = _run
	// 监听xUseRequest返回的loading状态，同步到组件级别的loading
	watchEffect(() => {
		requestLoading.value = loading.value
	})
	data = _data
}

/**
 * 获取路线详情
 * @param id 路线ID
 */
async function getDetail(id: number) {
	detailLoading.value = true
	const res = await BIZ_JnrmRoute_APIS.get(id).finally(() => (detailLoading.value = false))
	Object.assign(model.value, res)

	// 如果有出发地和目的地ID，获取完整的location信息
	if (res.departureId) {
		await getLocationDetail(res.departureId, 'departure')
	}
	if (res.destinationId) {
		await getLocationDetail(res.destinationId, 'destination')
	}
}

/**
 * 获取location详细信息
 * @param locationId location ID
 * @param type 类型：'departure' 或 'destination'
 */
async function getLocationDetail(locationId: number, type: 'departure' | 'destination') {
	try {
		const locationInfo = await BIZ_JnrmLocation_APIS.get(locationId)
		if (type === 'departure') {
			departureLocation.value = locationInfo
		} else {
			destinationLocation.value = locationInfo
		}
		console.log(`${type === 'departure' ? '出发地' : '目的地'}信息:`, locationInfo)
	} catch (error) {
		console.error(`获取${type === 'departure' ? '出发地' : '目的地'}信息失败:`, error)
	}
}

/**
 * 处理出发地选择变化
 * @param value 选中的出发地ID
 */
async function handleDepartureChange(value: number) {
	if (value) {
		await getLocationDetail(value, 'departure')
	} else {
		departureLocation.value = null
	}
}

/**
 * 处理目的地选择变化
 * @param value 选中的目的地ID
 */
async function handleDestinationChange(value: number) {
	if (value) {
		await getLocationDetail(value, 'destination')
	} else {
		destinationLocation.value = null
	}
}

/**
 * 获取路线推荐
 */
async function getRouteRecommendation(): Promise<void> {
	if (!canGetRecommendation.value) {
		toast.warning('请先选择出发地和目的地')
		return
	}

	routeLoading.value = true
	try {
		const origin = `${departureLocation.value!.latitude},${departureLocation.value!.longitude}`
		const destination = `${destinationLocation.value!.latitude},${destinationLocation.value!.longitude}`
		const ak = 'ilfyAqdRqg2NOdGudMZATdj4DN5WvO5k' // import.meta.env.BD_MAP_AK

		// const url = `https://api.map.baidu.com/logistics_direction/v1/truck?ak=${ak}&origin=${origin}&destination=${destination}`
		const url = `https://api.map.baidu.com/direction/v2/driving?ak=${ak}&origin=${origin}&destination=${destination}`

		const response = await fetch(url)
		const data: RouteResponse = await response.json()

		if (data.status === 0 && data.result.routes.length > 0) {
			recommendedRoutes.value = data.result.routes
			selectedRouteIndex.value = -1

			// 初始化地图
			nextTick(() => {
				initRouteMap()
				displayAllRoutes()
			})

			toast.success(`获取到 ${data.result.routes.length} 条推荐路线`)
		} else {
			toast.error('获取路线推荐失败：' + (data.message || '未知错误'))
		}
	} catch (error) {
		console.error('获取路线推荐失败:', error)
		toast.error('获取路线推荐失败，请检查网络连接')
	} finally {
		routeLoading.value = false
	}
}

/**
 * 初始化路线地图
 */
function initRouteMap(): void {
	if (typeof window.BMapGL === 'undefined') {
		console.error('百度地图API未加载')
		return
	}

	// 创建地图实例
	map = new window.BMapGL.Map('routeMap', { enableMapClick: false })

	// 计算地图中心点（出发地和目的地的中点）
	const centerLng = (parseFloat(departureLocation.value!.longitude) + parseFloat(destinationLocation.value!.longitude)) / 2
	const centerLat = (parseFloat(departureLocation.value!.latitude) + parseFloat(destinationLocation.value!.latitude)) / 2
	const centerPoint = new window.BMapGL.Point(centerLng, centerLat)

	// 初始化地图
	map.centerAndZoom(centerPoint, 11)
	map.enableScrollWheelZoom(true)

	// 添加出发地和目的地标记
	const startPoint = new window.BMapGL.Point(parseFloat(departureLocation.value!.longitude), parseFloat(departureLocation.value!.latitude))
	const endPoint = new window.BMapGL.Point(parseFloat(destinationLocation.value!.longitude), parseFloat(destinationLocation.value!.latitude))

	const startMarker = new window.BMapGL.Marker(startPoint)
	const endMarker = new window.BMapGL.Marker(endPoint)

	map.addOverlay(startMarker)
	map.addOverlay(endMarker)

	// 添加标签
	const startLabel = new window.BMapGL.Label(departureLocation.value!.name, { offset: new window.BMapGL.Size(20, -10) })
	const endLabel = new window.BMapGL.Label(destinationLocation.value!.name, { offset: new window.BMapGL.Size(20, -10) })

	startMarker.setLabel(startLabel)
	endMarker.setLabel(endLabel)
}

/**
 * 解析路径字符串为坐标点数组
 * @param pathString 路径字符串
 * @returns 坐标点数组
 */
function parsePathToPoints(pathString: string): any[] {
	const points: any[] = []
	const coords = pathString.split(';')

	for (const coord of coords) {
		const [lng, lat] = coord.split(',')
		if (lng && lat) {
			points.push(new window.BMapGL.Point(parseFloat(lng), parseFloat(lat)))
		}
	}

	return points
}

/**
 * 显示所有路线
 */
function displayAllRoutes(): void {
	if (!map) return

	// 清除之前的路线
	clearRoutePolylines()

	recommendedRoutes.value.forEach((route, index) => {
		const allPoints: any[] = []

		// 合并所有步骤的路径点
		route.steps.forEach((step) => {
			const stepPoints = parsePathToPoints(step.path)
			allPoints.push(...stepPoints)
		})

		if (allPoints.length > 0) {
			const polyline = new window.BMapGL.Polyline(allPoints, {
				strokeTexture: {
					url: 'https://mapopen-pub-jsapigl.bj.bcebos.com/svgmodel/Icon_road_blue_arrow.png',
					width: 16,
					height: 64,
				},
				strokeWeight: 6,
				strokeOpacity: 0.6,
				strokeColor: routeColors[index % routeColors.length],
			})

			map.addOverlay(polyline)
			routePolylines.push(polyline)
		}
	})

	// 调整地图视野以包含所有路线
	if (routePolylines.length > 0) {
		const allPoints: any[] = []
		routePolylines.forEach((polyline) => {
			allPoints.push(...polyline.getPath())
		})
		map.setViewport(allPoints)
	}
}

/**
 * 清除路线折线
 */
function clearRoutePolylines(): void {
	if (map && routePolylines.length > 0) {
		routePolylines.forEach((polyline) => {
			map.removeOverlay(polyline)
		})
		routePolylines = []
	}
}

/**
 * 选择路线
 * @param index 路线索引
 */
function selectRoute(index: number): void {
	selectedRouteIndex.value = index

	if (!map) return

	// 重新显示所有路线，但突出显示选中的路线
	clearRoutePolylines()

	recommendedRoutes.value.forEach((route, routeIndex) => {
		const allPoints: any[] = []

		// 合并所有步骤的路径点
		route.steps.forEach((step) => {
			const stepPoints = parsePathToPoints(step.path)
			allPoints.push(...stepPoints)
		})

		if (allPoints.length > 0) {
			const isSelected = routeIndex === index
			const polyline = new window.BMapGL.Polyline(allPoints, {
				strokeTexture: {
					url: 'https://mapopen-pub-jsapigl.bj.bcebos.com/svgmodel/Icon_road_blue_arrow.png',
					width: 16,
					height: 64,
				},
				strokeWeight: isSelected ? 10 : 4,
				strokeOpacity: isSelected ? 1 : 0.4,
				strokeColor: routeColors[routeIndex % routeColors.length],
			})

			map.addOverlay(polyline)
			routePolylines.push(polyline)
		}
	})
}

/**
 * 使用选中的路线
 * @param route 路线对象
 * @param index 路线索引
 */
function useRoute(route: Route, index: number): void {
	// 将路线数据填充到表单
	model.value.distance = (route.distance / 1000).toFixed(1) // 转换为公里
	model.value.estimatedDuration = Math.round(route.duration / 60).toString() // 转换为分钟
	model.value.routeName = `${departureLocation.value?.name} → ${destinationLocation.value?.name} (${route.tag})`

	// 将路线路径保存为JSON格式
	const routeData = {
		route: route,
		color: routeColors[index % routeColors.length],
		departureLocation: departureLocation.value,
		destinationLocation: destinationLocation.value,
	}
	model.value.trackPath = JSON.stringify(routeData)

	toast.success('路线信息已填充到表单')
}

// 监听模态框显示状态，清理资源
watch(visible, (newVal) => {
	if (!newVal) {
		// 模态框关闭时清理地图资源
		cleanupMapResources()
	}
})

/**
 * 清理地图资源
 */
function cleanupMapResources(): void {
	if (map) {
		clearRoutePolylines()
		map = null
	}
	recommendedRoutes.value = []
	selectedRouteIndex.value = -1
	routeLoading.value = false
}

defineExpose({ open })

/**
 * 处理表单提交
 */
async function handleSubmit() {
	if (!visible.value) return
	await run()
	visible.value = false
	if (data && type.value == 'create') {
		model.value.id = data
	}

	// 提交成功后，可以通过departureLocation和destinationLocation获取完整的location信息
	// 包含id、name、longitude、latitude等字段
	const result = {
		...model.value,
		departureLocation: departureLocation.value,
		destinationLocation: destinationLocation.value,
	}

	emit('success', result)
}
</script>

<style scoped>
/* 路线推荐按钮样式 */
.route-recommend-btn {
	padding: 12px 24px;
	background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
	color: white;
	border: none;
	border-radius: 8px;
	font-size: 14px;
	font-weight: 500;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.route-recommend-btn:hover:not(:disabled) {
	background: linear-gradient(135deg, #66b1ff 0%, #409eff 100%);
	transform: translateY(-2px);
	box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.route-recommend-btn:disabled {
	background: #c0c4cc;
	cursor: not-allowed;
	transform: none;
	box-shadow: none;
}

/* 路线列表样式 */
.route-list {
	background: #f8f9fa;
	border-radius: 8px;
	padding: 16px;
	height: 500px;
	overflow-y: auto;
}

.route-list-title {
	font-size: 16px;
	font-weight: 600;
	color: #303133;
	margin: 0 0 16px 0;
	padding-bottom: 8px;
	border-bottom: 2px solid #e4e7ed;
}

.route-items {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.route-item {
	background: white;
	border: 2px solid #e4e7ed;
	border-radius: 8px;
	padding: 16px;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
}

.route-item:hover {
	border-color: #409eff;
	box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
	transform: translateY(-2px);
}

.route-item-selected {
	border-color: #409eff;
	background: #f0f8ff;
	box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.route-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 12px;
}

.route-tag {
	padding: 4px 8px;
	border-radius: 4px;
	color: white;
	font-size: 12px;
	font-weight: 500;
}

.route-distance {
	font-size: 16px;
	font-weight: 600;
	color: #303133;
}

.route-info {
	display: flex;
	flex-direction: column;
	gap: 6px;
	margin-bottom: 12px;
}

.route-duration,
.route-cost,
.route-lights {
	font-size: 13px;
	color: #606266;
	display: flex;
	align-items: center;
}

.use-route-btn {
	width: 100%;
	padding: 8px 16px;
	background: #67c23a;
	color: white;
	border: none;
	border-radius: 6px;
	font-size: 13px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.use-route-btn:hover {
	background: #85ce61;
	transform: translateY(-1px);
}

/* 地图容器样式 */
.map-container {
	width: 100%;
	height: 500px;
	border: 1px solid #e4e7ed;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.route-map {
	width: 100%;
	height: 100%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
	.grid-cols-3 {
		grid-template-columns: 1fr;
	}

	.col-span-2 {
		grid-column: span 1;
	}

	.route-list {
		height: 300px;
		margin-bottom: 16px;
	}

	.map-container {
		height: 400px;
	}
}

/* 滚动条样式 */
.route-list::-webkit-scrollbar {
	width: 6px;
}

.route-list::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 3px;
}

.route-list::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 3px;
}

.route-list::-webkit-scrollbar-thumb:hover {
	background: #a8a8a8;
}
</style>
