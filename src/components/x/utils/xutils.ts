import dayjs from 'dayjs'
import { v4 as uuidv4 } from 'uuid'
import lodash from 'lodash'

export const X_DATE_UTILS = {
	formatDate,
	/**
	 * 获取今天的时间范围（毫秒时间戳）
	 * @returns {number[]} [今天开始时间戳, 今天结束时间戳]
	 */
	getTodayRange(): number[] {
		const today = dayjs()
		const startOfDay = today.startOf('day').valueOf() // 今天 00:00:00.000 的毫秒时间戳
		const endOfDay = today.endOf('day').valueOf() // 今天 23:59:59.999 的毫秒时间戳
		return [startOfDay, endOfDay]
	},
	/**
	 * 获取昨日时间范围（毫秒时间戳）
	 */
	getYesterdayRange(): number[] {
		const y = dayjs().subtract(1, 'day')
		const start = y.startOf('day').valueOf()
		const end = y.endOf('day').valueOf()
		return [start, end]
	},

	/**
	 * 获取近7天时间范围（含今天，共7天，毫秒时间戳）
	 */
	getLast7DaysRange(): number[] {
		const end = dayjs().endOf('day')
		const start = end.subtract(6, 'day').startOf('day')
		return [start.valueOf(), end.valueOf()]
	},

	/**
	 * 获取本季度时间范围（毫秒时间戳）
	 */
	getThisQuarterRange(): number[] {
		const now = dayjs()
		const quarter = Math.floor(now.month() / 3) // 0..3
		const startMonth = quarter * 3
		const start = now.month(startMonth).startOf('month').startOf('day')
		const end = start.add(2, 'month').endOf('month').endOf('day')
		return [start.valueOf(), end.valueOf()]
	},

	/**
	 * 获取近30天时间范围（含今天，共30天，毫秒时间戳）
	 */
	getLast30DaysRange(): number[] {
		const end = dayjs().endOf('day')
		const start = end.subtract(29, 'day').startOf('day')
		return [start.valueOf(), end.valueOf()]
	},

	/**
	 * 获取本周时间范围（周一至周日，毫秒时间戳）
	 */
	getThisWeekRange(): number[] {
		const today = dayjs()
		const day = today.day() // 0=周日,1=周一,...6=周六
		const monday = today.subtract((day + 6) % 7, 'day').startOf('day')
		const sunday = monday.add(6, 'day').endOf('day')
		return [monday.valueOf(), sunday.valueOf()]
	},
	/**
	 * 获取本月时间范围（毫秒时间戳）
	 */
	getThisMonthRange(): number[] {
		const today = dayjs()
		const start = today.startOf('month').startOf('day')
		const end = today.endOf('month').endOf('day')
		return [start.valueOf(), end.valueOf()]
	},
	getSecondsDiffFromHourStart(timestamp) {
		// 解析时间戳
		const time = dayjs(timestamp)
		// 获取整点时间（分钟和秒设为0）
		const hourStart = time.startOf('hour')
		// 计算差值（秒数）
		const diffInSeconds = time.diff(hourStart, 'second')

		return diffInSeconds
	},
	formatSeconds: (seconds: number, splits = '::') => {
		if (!(seconds > 0)) return `00${splits?.[0]}00${splits?.[1]}00${splits?.[2] ? splits?.[2] : ''}`

		const hours = Math.floor(seconds / 3600)
		const minutes = Math.floor((seconds % 3600) / 60)
		const remainingSeconds = seconds % 60
		const hour = hours.toString().padStart(2, '0')
		const minute = minutes.toString().padStart(2, '0')
		const second = remainingSeconds.toString().padStart(2, '0')
		return `${hour}${splits?.[0]}${minute}${splits?.[1]}${second}${splits?.[2] ? splits?.[2] : ''}`
	},
	getRemainingTime: (endTime: string | number) => {
		if (!endTime) return ''
		const end = new Date(endTime).getTime()
		const now = new Date().getTime()
		const diff = end - now

		if (diff <= 0) return '已截止'

		const days = Math.floor(diff / (1000 * 60 * 60 * 24))
		const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
		const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

		if (days > 0) return `${days}天${hours}小时`
		if (hours > 0) return `${hours}小时${minutes}分钟`
		return `${minutes}分钟`
	},
}

export const X_FILE_UTILS = {
	formatUrlFileSize: (url) => {
		const match = url.match(/[?&]fz=(\d+)/)
		if (match) {
			const fileSize = parseInt(match[1])
			return X_FILE_UTILS.formatFileSize(fileSize)
		}
		return ''
	},
	formatFileSize: (bytes) => {
		if (bytes === 0) return '0 B'
		const k = 1024
		const sizes = ['B', 'KB', 'MB', 'GB']
		const i = Math.floor(Math.log(bytes) / Math.log(k))
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
	},
	getTruncatedName: (fileName: string, maxLength = 15) => {
		if (!fileName) return ''
		const extension = X_FILE_UTILS.getUrlExtension(fileName)
		const nameWithoutExt = fileName.substring(0, fileName.length - extension.length - 1)
		if (nameWithoutExt.length <= maxLength) return fileName
		return nameWithoutExt.substring(0, maxLength) + '...' + extension
	},
	getUrlExtension: (url: string) => {
		if (!url) return ''
		// 移除查询参数部分
		const urlWithoutParams = url.split('?')[0]

		// 获取URL的最后一个路径段
		const lastPart = urlWithoutParams.split('/').pop() || ''

		// 提取扩展名
		const lastDotIndex = lastPart.lastIndexOf('.')
		if (lastDotIndex === -1) return ''

		return lastPart.substring(lastDotIndex + 1).toLowerCase()
	},
	getUrlWithoutExtension: (url: string) => {
		if (!url) return ''
		// 移除查询参数部分
		const urlWithoutParams = url.split('?')[0]

		// 处理最后一个路径段落
		const parts = urlWithoutParams.split('/')
		const lastPart = parts.pop() || ''

		// 查找最后一个路径段中的最后一个点号位置
		const lastDotIndex = lastPart.lastIndexOf('.')

		if (lastDotIndex === -1) {
			// 如果没有扩展名，直接返回完整的URL（不含参数）
			return urlWithoutParams
		}

		// 组合URL前缀和文件名部分（不含扩展名）
		const prefix = parts.length > 0 ? parts.join('/') + '/' : ''
		const fileNameWithoutExt = lastPart.substring(0, lastDotIndex)
		return prefix + fileNameWithoutExt
	},
	isImageExtension(fileName: string, _extension) {
		const extension = typeof _extension === 'string' ? _extension : X_FILE_UTILS.getUrlExtension(fileName)
		return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(extension)
	},
	upload: (file, uploadUrl = import.meta.env.VITE_UPLOAD_BASE_URL) => {
		const userStore = useUserStore()
		return new Promise((resolve, reject) => {
			if (file.status === 'success') {
				resolve()
			}
			const fileSize = file.raw?.size || file?.size

			// 检查文件大小，如果传入了maxSize参数，则检查文件大小是否超过限制
			const maxSize = file?.maxSize ? file.maxSize : 0.3
			const maxSizeBytes = maxSize * 1024 * 1024
			if (fileSize && maxSize && fileSize > maxSizeBytes) {
				// toast.error(`文件大小超过限制，最大允许${maxSize}MB`)
				reject(new Error(`文件大小超过限制，最大允许${maxSize}MB`))
				return
			}

			const formData = new FormData()
			formData.append('file', file.raw, file?.name)

			const xhr = new XMLHttpRequest()

			// 监听上传进度
			xhr.upload.addEventListener('progress', (e) => {
				if (e.lengthComputable) {
					file.progress = Math.round((e.loaded / e.total) * 100)
				}
			})

			// 监听请求完成
			xhr.addEventListener('load', () => {
				if (xhr.status >= 200 && xhr.status < 300) {
					try {
						const response = JSON.parse(xhr.responseText)
						if (response.success || response?.code === 0) {
							// 如果文件有大小，将大小信息添加到URL中
							if (fileSize) {
								const fileUrl = response.data
								const separator = fileUrl.includes('?') ? '&' : '?'
								// 使用文件的原始字节大小
								response.data = `${fileUrl}${separator}fz=${fileSize}`
							}
							file.remoteUrl = response.data
							resolve(response.data)
						} else {
							reject(new Error(response.message || response.msg || '服务器返回上传失败'))
						}
					} catch (error) {
						reject(new Error('解析服务器响应失败'))
					}
				} else {
					reject(new Error(`上传失败，状态码：${xhr.status} ${xhr.statusText}`))
				}
			})

			// 监听请求错误
			xhr.addEventListener('error', () => {
				reject(new Error('网络错误，请检查网络连接'))
			})

			// 监听请求超时
			xhr.addEventListener('timeout', () => {
				reject(new Error('请求超时，请稍后重试'))
			})

			// 监听请求中止
			xhr.addEventListener('abort', () => {
				reject(new Error('上传已取消'))
			})

			// 设置超时时间（默认10秒）
			xhr.timeout = 3 * 60 * 1000

			// 打开请求
			xhr.open('POST', uploadUrl, true)
			if (userStore.token) {
				xhr.setRequestHeader('Authorization', 'Bearer ' + userStore.token)
			}
			// 设置请求头（如果需要）
			const token = localStorage.getItem('token')
			if (token) {
				xhr.setRequestHeader('Authorization', `Bearer ${token}`)
			}

			// 发送请求
			xhr.send(formData)
		})
	},
	uploadUser: (file) => X_FILE_UTILS.upload(file, import.meta.env.VITE_UPLOAD_USER_BASE_URL),
}

export const X_COMMON_UTILS = {
	isArray(value: any): value is Array<any> {
		return lodash.isArray(value)
	},
	isFunction(value: any): value is Function {
		return lodash.isFunction(value)
	},
	isObject(value: any): value is Record<string, any> {
		return lodash.isObject(value)
	},
	isString(value: unknown): value is string {
		return lodash.isString(value)
	},
	isNumber(value: any): value is number {
		return lodash.isNumber(value)
	},
	isDef(value: any): value is NonNullable {
		return value !== undefined && value !== null
	},
	isBoolean(value: any): value is boolean {
		return lodash.isBoolean(value)
	},
	removeHtmlTags: (html) => {
		// 替换 &nbsp; 和其他常见 HTML 实体为空格
		html = html.replace(/&nbsp;|&ensp;|&emsp;/gi, ' ')

		// 替换其他 HTML 实体为对应的字符（可选）
		html = html.replace(/&lt;/gi, '<')
		html = html.replace(/&gt;/gi, '>')
		html = html.replace(/&amp;/gi, '&')
		html = html.replace(/&quot;/gi, '"')
		html = html.replace(/&apos;/gi, "'")

		// 移除所有 HTML 标签
		html = html.replace(/<[^>]*>/g, '')

		// 合并多个连续空格为一个空格
		html = html.replace(/\s+/g, ' ').trim()

		return html
	},
	generateUUID: uuidv4,
	// 将扁平数据转换为树形结构
	flattenToTree: (data, idField = 'id', parentIdField = 'parentId', childrenField = 'children') => {
		// 创建一个映射，用于快速查找节点
		const map = {}
		const tree = []

		// 构建节点映射
		data.forEach((item) => {
			map[item[idField]] = { ...item, [childrenField]: [] }
		})

		// 构建树形结构
		data.forEach((item) => {
			const node = map[item[idField]]
			const parentId = item[parentIdField]

			if (parentId && map[parentId]) {
				// 如果有父节点，则将当前节点添加到父节点的children中
				map[parentId][childrenField].push(node)
			} else {
				// 如果没有父节点或父节点不存在，则作为根节点
				tree.push(node)
			}
		})

		return tree
	},
}

export const X_MATH_UTILS = {
	safeCalculateExpression(expression, decimalPlaces = 2, shouldRound = false) {
		const result = X_MATH_UTILS.calculateExpression(expression, decimalPlaces, shouldRound)
		return result === null ? 0 : result
	},
	calculateExpression(expression, decimalPlaces = 2, shouldRound = false) {
		try {
			// 清理表达式，移除任何非数学运算符和数字的内容
			// 只保留数字、基本运算符、小数点和括号
			let cleanExpression = String(expression).replace(/[^0-9+\-*/().]/g, '')
			// 检查括号是否平衡
			let openBrackets = 0
			for (let i = 0; i < cleanExpression.length; i++) {
				if (cleanExpression[i] === '(') openBrackets++
				else if (cleanExpression[i] === ')') openBrackets--

				// 如果右括号多于左括号，表达式无效
				if (openBrackets < 0) {
					console.warn('表达式括号不平衡:', expression)
					return null
				}
			}

			// 添加缺失的右括号
			if (openBrackets > 0) {
				cleanExpression += ')'.repeat(openBrackets)
			}

			// 检查表达式是否为空
			if (!cleanExpression.trim()) {
				return null
			}

			// 验证表达式是否有效
			// 检查是否以运算符结尾(除了右括号)
			if (/[+\-*/]$/.test(cleanExpression)) {
				cleanExpression += '0'
			}

			// 检查是否以运算符开头(除了左括号和减号)
			if (/^[+*/]/.test(cleanExpression)) {
				cleanExpression = '0' + cleanExpression
			}

			// 替换连续的运算符为最后一个运算符
			cleanExpression = cleanExpression.replace(/([+\-*/])([+*/])+/g, '$2')

			// 安全地计算表达式
			// eslint-disable-next-line no-new-func
			const result = new Function('return ' + cleanExpression)()

			// 检查结果是否为有效数字
			if (isNaN(result) || !isFinite(result)) {
				console.warn('计算结果不是有效数字:', result)
				return null
			}

			// 根据shouldRound参数处理舍入方式
			if (shouldRound) {
				// 精度修正算法（处理 2.675 → 2.68 类问题）
				const adjustNumber = (n) => {
					const isNegative = n < 0
					const absN = Math.abs(n)
					const precisionFactor = Math.pow(10, decimalPlaces + 1)
					const scaled = Math.round(absN * precisionFactor)
					const base = Math.floor(scaled / 10)
					const remainder = scaled % 10
					const adjusted = (base + (remainder >= 5 ? 1 : 0)) / (precisionFactor / 10)
					return isNegative ? -adjusted : adjusted
				}

				// 使用四舍五入方式
				const processed = adjustNumber(result)
				return decimalPlaces === 0
					? Math.round(processed) // 整数直接取整
					: Number(processed.toFixed(Math.min(decimalPlaces, 15)))
			} else {
				// 使用向下取整方式（原有逻辑）
				const factor = Math.pow(10, decimalPlaces)
				return Math.round(result * factor) / factor
			}
		} catch (error) {
			console.error('计算表达式错误:', error, '表达式:', expression)
			return null
		}
	},
}

function formatDate(date, format) {
	//检测format是否为合格的格式
	if (!format || typeof format !== 'string') {
		format = 'YYYY-MM-DD HH:mm'
	}
	// 如果是无效的时间
	if (!date || !dayjs(date).isValid()) {
		return date
	}
	return dayjs(date).format(format)
}
export const systemTime = ref(formatDate())

export function setIntervalSystemTime() {
	setInterval(() => {
		systemTime.value = formatDate()
	}, 1000)
}

setIntervalSystemTime()
const download0 = (data: Blob, fileName: string, mineType: string) => {
	// 创建 blob
	const blob = new Blob([data], { type: mineType })
	// 创建 href 超链接，点击进行下载
	window.URL = window.URL || window.webkitURL
	const href = URL.createObjectURL(blob)
	const downA = document.createElement('a')
	downA.href = href
	downA.download = fileName
	downA.click()
	// 销毁超连接
	window.URL.revokeObjectURL(href)
}
export const X_DOWNLOAD_UTILS = {
	excel: (data: Blob, fileName: string) => {
		download0(data, fileName, 'application/vnd.ms-excel')
	},
	// 下载 Word 方法
	word: (data: Blob, fileName: string) => {
		download0(data, fileName, 'application/msword')
	},
	// 下载 Zip 方法
	zip: (data: Blob, fileName: string) => {
		download0(data, fileName, 'application/zip')
	},
	// 下载 Html 方法
	html: (data: Blob, fileName: string) => {
		download0(data, fileName, 'text/html')
	},
	// 下载 Markdown 方法
	markdown: (data: Blob, fileName: string) => {
		download0(data, fileName, 'text/markdown')
	},
}
