<route lang="json">
{
	"name": "adminHome",
	"meta": {
		"title": "欢迎页"
	},
	"redirect": "/common-announcement"
}
</route>
<script setup lang="ts">
// 用户信息
const userInfo = ref({
	name: '管理员',
	avatar: 'https://avatars.githubusercontent.com/u/11247099',
	lastLogin: new Date().toLocaleString(),
})

// 系统信息
const systemInfo = ref({
	name: '企业级管理系统',
	version: 'v1.0.0',
	uptime: '99.9%',
	lastUpdate: '2024-03-20',
	securityLevel: '高',
})

// 统计数据
const stats = ref([
	{ title: '总用户数', value: '12,846', icon: 'i-carbon:user-multiple', color: 'bg-gradient-to-r from-blue-500 to-blue-400', trend: '+12.5%' },
	{ title: '今日活跃', value: '2,534', icon: 'i-carbon:user-online', color: 'bg-gradient-to-r from-violet-500 to-indigo-400', trend: '+8.2%' },
	{ title: '总订单数', value: '45,293', icon: 'i-carbon:document', color: 'bg-gradient-to-r from-amber-500 to-yellow-400', trend: '+15.3%' },
	{ title: '系统消息', value: '18', icon: 'i-carbon:notification', color: 'bg-gradient-to-r from-emerald-500 to-teal-400', trend: '-2.1%' },
	{ title: '在线设备', value: '1,287', icon: 'i-carbon:device', color: 'bg-gradient-to-r from-rose-500 to-pink-400', trend: '+5.7%' },
])

// 系统公告
const announcements = ref([
	{ title: '系统升级通知', content: '系统将于本周日凌晨2:00-4:00进行例行维护升级', type: 'info', time: '2024-03-20' },
	{ title: '安全提醒', content: '请及时更新您的密码，确保账户安全', type: 'warning', time: '2024-03-19' },
	{ title: '新功能上线', content: '数据分析模块全新上线，支持更多可视化图表', type: 'success', time: '2024-03-18' },
])

// 待办事项
const todos = ref([
	{ title: '审核新用户申请', priority: 'high', deadline: '今天 14:00' },
	{ title: '更新系统配置', priority: 'medium', deadline: '明天 10:00' },
	{ title: '生成月度报表', priority: 'low', deadline: '本周五' },
])

// 最近活动
const activities = ref([
	{ user: '张三', action: '创建了新订单', time: '10分钟前', type: 'order' },
	{ user: '李四', action: '更新了用户信息', time: '30分钟前', type: 'user' },
	{ user: '王五', action: '删除了过期数据', time: '1小时前', type: 'system' },
	{ user: '赵六', action: '导出了月度报表', time: '2小时前', type: 'report' },
])

// 数据趋势
const trends = ref([
	{ name: '用户增长', data: [65, 78, 90, 85, 99, 105, 98] },
	{ name: '订单量', data: [28, 32, 35, 30, 38, 42, 40] },
	{ name: '系统负载', data: [45, 52, 48, 55, 50, 58, 54] },
])

// 动画效果
const isLoaded = ref(false)
const showSimpleView = ref(true) // 控制显示哪个视图

// 预生成随机圆球数据，避免路由切换时重新渲染
const circleData = ref([])

// 背景装饰元素
const circles = ref([])
const pageLoaded = ref(false)

// 生成随机圆形
const generateCircles = () => {
	const colors = ['#4f46e5', '#6366f1', '#818cf8', '#a5b4fc', '#3b82f6', '#60a5fa', '#93c5fd']
	const minCircles = 8
	const maxCircles = 12
	const circleCount = Math.floor(Math.random() * (maxCircles - minCircles + 1)) + minCircles

	const newCircles = []

	for (let i = 0; i < circleCount; i++) {
		const size = Math.floor(Math.random() * 150) + 50 // 随机大小在50px到200px之间
		const left = Math.floor(Math.random() * 80) + 10 // 随机左侧位置在10%到90%之间
		const top = Math.floor(Math.random() * 80) + 10 // 随机顶部位置在10%到90%之间
		const delay = Math.floor(Math.random() * 5) // 随机延迟在0s到5s之间
		const duration = Math.floor(Math.random() * 20) + 15 // 随机持续时间在15s到35s之间
		const color = colors[Math.floor(Math.random() * colors.length)] // 从数组中随机选择颜色

		newCircles.push({
			id: i,
			size,
			left,
			top,
			delay,
			duration,
			color,
		})
	}

	circles.value = newCircles
}

onMounted(() => {
	setTimeout(() => {
		isLoaded.value = true
	}, 300)

	// 生成随机圆形
	generateCircles()

	// 组件挂载后触发动画
	setTimeout(() => {
		pageLoaded.value = true
	}, 100)
})
</script>

<template>
	<div v-if="!showSimpleView" class="welcome-page h-screen overflow-hidden from-blue-50 via-white to-indigo-50 bg-gradient-to-br p-4 text-gray-700">
		<div class="grid grid-rows-[auto_auto_1fr] h-full gap-4">
			<!-- 欢迎区域 -->
			<div
				class="welcome-header border border-blue-100 rounded-xl bg-white/80 p-4 shadow-blue-100/50 shadow-xl backdrop-blur-sm transition-all duration-700"
				:class="{ 'opacity-100 transform-none': isLoaded, 'opacity-0 translate-y-4': !isLoaded }"
			>
				<div class="flex items-center justify-between">
					<div class="flex items-center gap-4">
						<div
							class="avatar h-16 w-16 flex items-center justify-center overflow-hidden rounded-full from-blue-500 to-indigo-600 bg-gradient-to-r shadow-lg"
						>
							<img :src="userInfo.avatar" alt="Avatar" class="h-full w-full object-cover" />
						</div>
						<div>
							<h1 class="from-blue-600 to-indigo-700 bg-gradient-to-r bg-clip-text text-transparent font-bold text-3xl">
								欢迎回来，{{ userInfo.name }}
							</h1>
							<p class="mt-1 text-gray-500">上次登录: {{ userInfo.lastLogin }}</p>
						</div>
					</div>
					<div class="text-right">
						<h2 class="text-gray-800 font-semibold text-xl">{{ systemInfo.name }}</h2>
						<div class="mt-1 flex items-center justify-end gap-2">
							<span class="text-gray-500">{{ systemInfo.version }}</span>
							<div class="flex items-center">
								<span class="mr-1 h-2 w-2 rounded-full bg-green-500"></span>
								<span class="text-gray-500">运行状态: {{ systemInfo.uptime }}</span>
							</div>
						</div>
						<div class="mt-2 flex items-center justify-end gap-2 text-sm">
							<span class="text-gray-500">最后更新: {{ systemInfo.lastUpdate }}</span>
							<span class="text-gray-500">安全等级: {{ systemInfo.securityLevel }}</span>
						</div>
					</div>
				</div>
			</div>

			<!-- 统计卡片 -->
			<div class="grid grid-cols-5 gap-4">
				<div
					v-for="(stat, index) in stats"
					:key="index"
					class="stat-card border border-blue-100 rounded-xl bg-white/80 p-4 shadow-lg transition-all duration-700 hover:shadow-xl hover:-translate-y-1"
					:class="[
						{
							'opacity-100 transform-none': isLoaded,
							'opacity-0 translate-y-4': !isLoaded,
							'transition-delay-100': index === 0,
							'transition-delay-200': index === 1,
							'transition-delay-300': index === 2,
							'transition-delay-400': index === 3,
							'transition-delay-500': index === 4,
						},
					]"
				>
					<div class="flex items-center justify-between">
						<div>
							<p class="text-gray-500 font-medium text-sm">{{ stat.title }}</p>
							<p class="mt-1 text-gray-800 font-bold text-2xl">{{ stat.value }}</p>
							<div class="mt-2 flex items-center gap-1">
								<span :class="stat.trend.startsWith('+') ? 'text-green-500' : 'text-red-500'">
									{{ stat.trend }}
								</span>
								<span class="text-gray-400 text-sm">较上周</span>
							</div>
						</div>
						<div :class="[stat.color, 'h-10 w-10 flex items-center justify-center rounded-full shadow-md']">
							<div :class="[stat.icon, 'text-xl text-white']"></div>
						</div>
					</div>
				</div>
			</div>

			<!-- 内容区域 -->
			<div class="grid grid-cols-1 gap-4 lg:grid-cols-3 xl:grid-cols-12">
				<!-- 左侧面板 -->
				<div
					class="border border-blue-100 rounded-xl bg-white/80 p-4 shadow-lg backdrop-blur-sm transition-all duration-700 lg:col-span-2 xl:col-span-8"
					:class="{ 'opacity-100 transform-none': isLoaded, 'opacity-0 translate-y-4': !isLoaded }"
				>
					<div class="grid grid-rows-[auto_1fr_auto] h-full gap-4">
						<!-- 标题 -->
						<div class="flex items-center justify-between">
							<h2 class="text-gray-800 font-semibold text-xl">系统概览</h2>
							<button class="text-blue-500 transition-colors text-sm hover:text-blue-700">查看详情</button>
						</div>

						<!-- 图表和状态指标 -->
						<div class="grid grid-rows-[1fr_auto] gap-4">
							<!-- 数据趋势图表 -->
							<div class="relative overflow-hidden border border-blue-100 rounded-lg from-blue-50 to-indigo-50 bg-gradient-to-br shadow-inner">
								<div class="absolute inset-0 flex items-center justify-center">
									<div class="text-center">
										<div class="mb-2 from-blue-600 to-indigo-700 bg-gradient-to-r bg-clip-text text-5xl text-transparent font-bold">87%</div>
										<p class="text-gray-500">系统性能</p>
									</div>
								</div>

								<svg class="absolute bottom-0 left-0 h-24 w-full opacity-20" viewBox="0 0 1200 200" xmlns="http://www.w3.org/2000/svg">
									<path fill="rgba(59, 130, 246, 0.5)" d="M0 50 Q 300 0, 600 50 T 1200 50 V200 H0 Z" />
									<path fill="rgba(99, 102, 241, 0.5)" d="M0 70 Q 300 20, 600 70 T 1200 70 V200 H0 Z" />
									<path fill="rgba(16, 185, 129, 0.5)" d="M0 90 Q 300 40, 600 90 T 1200 90 V200 H0 Z" />
								</svg>
							</div>

							<!-- 系统状态指标 -->
							<div class="grid grid-cols-3 gap-4">
								<div class="border border-blue-100 rounded-lg bg-white/80 p-4 shadow-md">
									<div class="text-gray-500 text-sm">CPU 使用率</div>
									<div class="mt-1 text-gray-800 font-bold text-2xl">42%</div>
									<div class="mt-2 h-1 w-full rounded-full bg-gray-200">
										<div class="h-1 rounded-full bg-blue-500" style="width: 42%"></div>
									</div>
								</div>
								<div class="border border-blue-100 rounded-lg bg-white/80 p-4 shadow-md">
									<div class="text-gray-500 text-sm">内存使用率</div>
									<div class="mt-1 text-gray-800 font-bold text-2xl">68%</div>
									<div class="mt-2 h-1 w-full rounded-full bg-gray-200">
										<div class="h-1 rounded-full bg-indigo-500" style="width: 68%"></div>
									</div>
								</div>
								<div class="border border-blue-100 rounded-lg bg-white/80 p-4 shadow-md">
									<div class="text-gray-500 text-sm">存储使用率</div>
									<div class="mt-1 text-gray-800 font-bold text-2xl">23%</div>
									<div class="mt-2 h-1 w-full rounded-full bg-gray-200">
										<div class="h-1 rounded-full bg-emerald-500" style="width: 23%"></div>
									</div>
								</div>
							</div>
						</div>

						<!-- 系统公告 -->
						<div>
							<h3 class="mb-2 text-gray-800 font-medium text-lg">系统公告</h3>
							<div class="space-y-2">
								<div
									v-for="(announcement, index) in announcements"
									:key="index"
									class="flex items-start gap-3 rounded-lg p-3 transition-colors"
									:class="{
										'bg-blue-50': announcement.type === 'info',
										'bg-amber-50': announcement.type === 'warning',
										'bg-emerald-50': announcement.type === 'success',
									}"
								>
									<div
										class="h-8 w-8 flex items-center justify-center rounded-full"
										:class="{
											'bg-blue-500': announcement.type === 'info',
											'bg-amber-500': announcement.type === 'warning',
											'bg-emerald-500': announcement.type === 'success',
										}"
									>
										<div
											class="text-white"
											:class="{
												'i-carbon:information': announcement.type === 'info',
												'i-carbon:warning': announcement.type === 'warning',
												'i-carbon:checkmark': announcement.type === 'success',
											}"
										></div>
									</div>
									<div class="flex-1">
										<div class="flex justify-between">
											<span class="font-medium">{{ announcement.title }}</span>
											<span class="text-gray-400 text-xs">{{ announcement.time }}</span>
										</div>
										<p class="mt-1 text-sm">{{ announcement.content }}</p>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 右侧面板 -->
				<div
					class="border border-blue-100 rounded-xl bg-white/80 p-4 shadow-lg backdrop-blur-sm transition-all duration-700 lg:col-span-1 xl:col-span-4"
					:class="{ 'opacity-100 transform-none': isLoaded, 'opacity-0 translate-y-4': !isLoaded }"
				>
					<div class="grid grid-rows-[auto_1fr_auto] h-full gap-4">
						<!-- 待办事项 -->
						<div>
							<div class="mb-2 flex items-center justify-between">
								<h2 class="text-gray-800 font-semibold text-xl">待办事项</h2>
								<button class="text-blue-500 transition-colors text-sm hover:text-blue-700">查看全部</button>
							</div>
							<div class="space-y-2">
								<div
									v-for="(todo, index) in todos"
									:key="index"
									class="flex items-center justify-between border border-blue-100 rounded-lg bg-white p-3 shadow-sm"
								>
									<div class="flex items-center gap-3">
										<div
											class="h-2 w-2 rounded-full"
											:class="{
												'bg-red-500': todo.priority === 'high',
												'bg-amber-500': todo.priority === 'medium',
												'bg-green-500': todo.priority === 'low',
											}"
										></div>
										<span class="text-gray-700">{{ todo.title }}</span>
									</div>
									<span class="text-gray-400 text-sm">{{ todo.deadline }}</span>
								</div>
							</div>
						</div>

						<!-- 最近活动 -->
						<div>
							<div class="mb-2 flex items-center justify-between">
								<h2 class="text-gray-800 font-semibold text-xl">最近活动</h2>
								<button class="text-blue-500 transition-colors text-sm hover:text-blue-700">查看全部</button>
							</div>
							<div class="space-y-2">
								<div
									v-for="(activity, index) in activities"
									:key="index"
									class="flex items-start gap-3 rounded-lg p-3 transition-colors hover:bg-blue-50"
								>
									<div
										class="h-8 w-8 flex items-center justify-center rounded-full from-blue-500 to-indigo-600 bg-gradient-to-r text-white font-bold shadow-md"
									>
										{{ activity.user.charAt(0) }}
									</div>
									<div class="flex-1">
										<div class="flex justify-between">
											<span class="text-gray-800 font-medium">{{ activity.user }}</span>
											<span class="text-gray-400 text-xs">{{ activity.time }}</span>
										</div>
										<p class="mt-1 text-gray-500 text-sm">{{ activity.action }}</p>
									</div>
								</div>
							</div>
						</div>

						<!-- 快速访问 -->
						<div class="border-blue-100 pt-4 border-t">
							<h3 class="mb-2 text-gray-800 font-medium text-lg">快速访问</h3>
							<div class="grid grid-cols-2 gap-2">
								<button class="border border-blue-100 rounded-lg bg-white p-3 text-left shadow-md transition-colors hover:bg-blue-50">
									<div class="mb-1 text-blue-600 font-medium">用户管理</div>
									<div class="text-gray-500 text-xs">管理系统用户</div>
								</button>
								<button class="border border-blue-100 rounded-lg bg-white p-3 text-left shadow-md transition-colors hover:bg-blue-50">
									<div class="mb-1 text-indigo-600 font-medium">数据分析</div>
									<div class="text-gray-500 text-xs">查看数据报表</div>
								</button>
								<button class="border border-blue-100 rounded-lg bg-white p-3 text-left shadow-md transition-colors hover:bg-blue-50">
									<div class="mb-1 text-emerald-600 font-medium">系统设置</div>
									<div class="text-gray-500 text-xs">配置系统参数</div>
								</button>
								<button class="border border-blue-100 rounded-lg bg-white p-3 text-left shadow-md transition-colors hover:bg-blue-50">
									<div class="mb-1 text-amber-600 font-medium">日志查询</div>
									<div class="text-gray-500 text-xs">查看系统日志</div>
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- 简洁酷炫的欢迎界面 -->
	<div v-else class="h-screen w-full overflow-hidden from-slate-50 via-blue-50 to-indigo-50 bg-gradient-to-br">
		<div class="relative h-full w-full">
			<!-- 动态背景 -->
			<div class="absolute inset-0">
				<!-- 圆形装饰 -->
				<div class="circles">
					<div
						v-for="circle in circles"
						:key="circle.id"
						class="circle"
						:style="{
							width: `${circle.size}px`,
							height: `${circle.size}px`,
							left: `${circle.left}%`,
							top: `${circle.top}%`,
							background: circle.color,
							animationDelay: `${circle.delay}s`,
							animationDuration: `${circle.duration}s`,
						}"
					></div>
				</div>
				<!-- 随机彩色圆球 - 使用预生成的数据 -->
				<div class="absolute inset-0 overflow-hidden">
					<div
						v-for="(circle, i) in circleData"
						:key="i"
						class="absolute rounded-full opacity-30"
						:class="[circle.bgClass, circle.animateClass]"
						:style="{
							width: circle.width,
							height: circle.height,
							left: circle.left,
							top: circle.top,
							animationDelay: circle.animationDelay,
						}"
					></div>
				</div>
				<div class="absolute inset-0 animate-pulse bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.05),transparent_50%)]"></div>
				<div class="absolute inset-0 animate-pulse bg-[radial-gradient(circle_at_80%_20%,rgba(139,92,246,0.05),transparent_50%)] delay-700"></div>
				<div class="absolute inset-0 animate-pulse bg-[radial-gradient(circle_at_20%_80%,rgba(168,85,247,0.05),transparent_50%)] delay-1400"></div>
			</div>

			<!-- 主要内容 -->
			<div class="relative z-10 h-full flex flex-col items-center justify-center">
				<div class="text-center">
					<h1 class="mb-6 animate-fade-in text-6xl font-bold tracking-wider">
						<span class="animate-gradient from-slate-800 via-blue-800 to-indigo-800 bg-gradient-to-r bg-clip-text text-transparent">
							{{ systemInfo.name }}
						</span>
					</h1>
					<p class="mb-8 animate-fade-in text-slate-700 font-light delay-300 text-2xl">
						欢迎回来，
						<span class="text-blue-700 font-medium">{{ userInfo.name }}</span>
					</p>
					<div class="animate-fade-in text-slate-600 delay-600 space-y-2">
						<p>Version: {{ systemInfo.version }}</p>
						<!--<p>上次登录: {{ userInfo.lastLogin }}</p>-->
					</div>
				</div>

				<!-- 装饰性元素 -->
				<div class="animate-float-1 absolute left-0 top-0 h-64 w-64 rounded-full bg-blue-200/20 blur-3xl"></div>
				<div class="animate-float-2 absolute bottom-0 right-0 h-64 w-64 rounded-full bg-indigo-200/20 blur-3xl"></div>
				<div class="animate-float-3 absolute right-1/4 top-1/4 h-32 w-32 rounded-full bg-slate-200/20 blur-3xl"></div>
				<div class="animate-float-1 absolute bottom-1/4 left-1/4 h-32 w-32 rounded-full bg-blue-200/20 blur-3xl"></div>

				<!-- 技术支持信息 -->
			</div>
		</div>
	</div>
</template>

<style scoped>
.welcome-page {
	background-image:
		radial-gradient(circle at 10% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 20%),
		radial-gradient(circle at 90% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 20%),
		radial-gradient(circle at 50% 50%, rgba(16, 185, 129, 0.05) 0%, transparent 30%);
}

.stat-card {
	position: relative;
	overflow: hidden;
}

.stat-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: inherit;
	opacity: 0.15;
	z-index: -1;
}

.transition-delay-100 {
	transition-delay: 100ms;
}
.transition-delay-200 {
	transition-delay: 200ms;
}
.transition-delay-300 {
	transition-delay: 300ms;
}
.transition-delay-400 {
	transition-delay: 400ms;
}
.transition-delay-500 {
	transition-delay: 500ms;
}

@keyframes gradient {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}

.animate-gradient {
	background-size: 200% auto;
	animation: gradient 8s linear infinite;
}

.delay-700 {
	animation-delay: 700ms;
}

@keyframes fadeIn {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.animate-fade-in {
	animation: fadeIn 1s ease-out forwards;
}

@keyframes float-1 {
	0%,
	100% {
		transform: translateY(0) translateX(0);
	}
	25% {
		transform: translateY(-10px) translateX(5px);
	}
	50% {
		transform: translateY(5px) translateX(10px);
	}
	75% {
		transform: translateY(10px) translateX(-5px);
	}
}

@keyframes float-2 {
	0%,
	100% {
		transform: translateY(0) translateX(0);
	}
	25% {
		transform: translateY(10px) translateX(-8px);
	}
	50% {
		transform: translateY(-5px) translateX(-12px);
	}
	75% {
		transform: translateY(-12px) translateX(8px);
	}
}

@keyframes float-3 {
	0%,
	100% {
		transform: translateY(0) translateX(0);
	}
	25% {
		transform: translateY(-15px) translateX(-10px);
	}
	50% {
		transform: translateY(8px) translateX(15px);
	}
	75% {
		transform: translateY(15px) translateX(5px);
	}
}

.animate-float-1 {
	animation: float-1 15s ease-in-out infinite;
}

.animate-float-2 {
	animation: float-2 18s ease-in-out infinite;
}

.animate-float-3 {
	animation: float-3 20s ease-in-out infinite;
}

@keyframes bounceSubtle {
	0%,
	100% {
		transform: translateY(0);
	}
	50% {
		transform: translateY(-5px);
	}
}

.animate-bounce-subtle {
	animation: bounceSubtle 2s ease-in-out infinite;
}

.delay-300 {
	animation-delay: 300ms;
}
.delay-600 {
	animation-delay: 600ms;
}
.delay-1000 {
	animation-delay: 1000ms;
}
.delay-1400 {
	animation-delay: 1400ms;
}
.delay-2000 {
	animation-delay: 2000ms;
}
.delay-3000 {
	animation-delay: 3000ms;
}

.bubble {
	animation: float 20s ease-in-out infinite;
	box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
	pointer-events: none;
	backdrop-filter: blur(8px);
	will-change: transform, opacity;
}

/* 圆形装饰 */
.circles {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
}

.circle {
	position: absolute;
	border-radius: 50%;
	opacity: 0.3;
	animation: circleAnimation 25s linear infinite;
	box-shadow: 0 0 15px rgba(79, 70, 229, 0.2);
	backdrop-filter: blur(8px);
}

@keyframes circleAnimation {
	0% {
		transform: translate(0, 0) rotate(0deg);
		opacity: 0.2;
		border-radius: 50%;
	}
	25% {
		transform: translate(50px, -30px) rotate(90deg);
		opacity: 0.25;
	}
	50% {
		transform: translate(0, -60px) rotate(180deg);
		opacity: 0.3;
		border-radius: 48%;
	}
	75% {
		transform: translate(-50px, -30px) rotate(270deg);
		opacity: 0.25;
	}
	100% {
		transform: translate(0, 0) rotate(360deg);
		opacity: 0.2;
		border-radius: 50%;
	}
}
</style>
