<route lang="json">
{
	"meta": {
		"title": "路线"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	departureId: null,
	destinationId: null,
	trackPath: null,
	distance: null,
	estimatedDuration: null,
	routeName: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmRoute_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmRoute_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '路线名称',
		prop: 'routeName',
	},
	{
		label: '出发地',
		prop: 'departureId',
	},
	{
		label: '目的地',
		prop: 'destinationId',
	},
	{
		label: '轨迹路径',
		prop: 'trackPath',
	},
	{
		label: '路线距离（公里）',
		prop: 'distance',
	},
	{
		label: '预估时长（分钟）',
		prop: 'estimatedDuration',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsJnrmRoute ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="路线名称" prop="routeName">
					<XInput v-model="queryModel.routeName" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="出发地" prop="departureId">
					<XInput v-model="queryModel.departureId" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="目的地" prop="destinationId">
					<XInput v-model="queryModel.destinationId" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
			</BizFormQueryForm>
			<div class="absolute bottom-0.2rem right-0.2rem flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('x:jnrm-route:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-route:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-route:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
