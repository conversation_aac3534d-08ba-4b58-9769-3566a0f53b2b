<route lang="json">
{
	"meta": {
		"title": "铅封"
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	sealNo: null,
	status: null,
	batteryLevel: null,
	serialNumber: null,
	createTime: [],
	...computedParams.value,
})

const sealQueryModel = reactive({
	pageIndex: 1,
	pageSize: 10,
	status: '-1',
	truckNo: null,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const loading = ref(false)
const list = ref([])
const total = ref(0)
async function loadList() {
	const res = await BIZ_JnrmSeal_APIS.getPageBySeal(sealQueryModel)
	console.log('res---', res)
	list.value = res?.list || []
	total.value = res?.total || 0
}
// const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmSeal_APIS.getPageBySeal, queryModel)
onMounted(() => {
	// run()
	loadList()
})

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '公司名称',
		prop: 'companyName',
		width: 180,
	},
	{
		label: '铅封编号',
		prop: 'deviceNo',
	},
	{
		label: '车牌号',
		prop: 'vehicleNumber',
	},
	{
		label: '铅封所处位置',
		prop: 'position',
	},
	{
		label: '状态',
		prop: 'statusName',
		// tagGroupName: 'JNRM铅封状态',
	},
	{
		label: '最后操作时间',
		prop: 'lastOprtTime',
	},
	{
		label: '最后操作地址',
		prop: 'address',
	},

	// {
	// 	label: '操作',
	// 	prop: 'action',
	// },
]
const BizFormQueryFormRef = ref()
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsJnrmSeal ref="modalRef" @success="loadList()" />
		<BizCardsQuery>
			<BizFormQueryForm ref="BizFormQueryFormRef" :model="sealQueryModel">
				<XFormItem label="车牌号" prop="truckNo">
					<XInput v-model="sealQueryModel.truckNo" placeholder="请输入搜索" @keyup.enter="loadList" />
				</XFormItem>
				<XFormItem label="状态" prop="status">
					<XSelect v-model="sealQueryModel.status" placeholder="请选择搜索" tag-group-name="JNRM铅封状态" tag-value-format="number" />
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset
					@click="
						() => {
							BizFormQueryFormRef?.reset()
							loadList()
						}
					"
				/>
				<BizButtonsQuery @click="loadList" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('x:jnrm-seal:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-seal:update')" @click="handleOpenModal('update', row.id)" />
						<!--						<BizButtonsDelete v-if="hasPermission('x:jnrm-seal:delete')" @click="handleDelete(row.id)" />-->
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="sealQueryModel.pageIndex" v-model:page-size="sealQueryModel.pageSize" :total="total" @change="loadList" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
