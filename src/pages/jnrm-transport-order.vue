<route lang="json">
{
	"meta": {
		"title": "承运单"
	}
}
</route>
<script setup lang="ts">
import JnrmAlert from '~/pages/jnrm-alert.vue'

const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
	searchable: {
		type: Boolean,
		default: true,
	},
	addable: {
		type: Boolean,
		default: true,
	},
	operable: {
		type: Boolean,
		default: true,
	},
	containerPlain: {
		type: Boolean,
		default: false,
	},
})
const userStore = useUserStore()
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	orderNo: null,
	deptId: null,
	powerPlantId: null,
	departureId: null,
	destinationId: null,
	vehicleId: null,
	driverId: null,
	licensePlate: null,
	driverName: null,
	driverPhone: null,
	cargoInfo: null,
	cargoWeight: null,
	startTime: [],
	deliveryTime: [],
	estimatedArrivalTime: [],
	actualArrivalTime: [],
	status: null,
	createTime: [],
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_JnrmTransportOrder_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_JnrmTransportOrder_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '运单号',
		prop: 'orderNo',
		width: 150,
	},
	{
		label: '出发地->目的地',
		prop: 'departureName',
		width: 250,
	},
	// {
	// 	label: '目的地',
	// 	prop: 'destinationName',
	// },
	{
		label: '车牌号',
		prop: 'licensePlate',
	},
	{
		label: '司机姓名',
		prop: 'driverName',
	},
	{
		label: '司机手机号',
		prop: 'driverPhone',
		width: 150,
	},
	// {
	// 	label: '货物信息',
	// 	prop: 'cargoInfo',
	// },
	// {
	// 	label: '货物重量(吨)',
	// 	prop: 'cargoWeight',
	// },
	{
		label: '开始时间',
		prop: 'startTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '预估送达时间',
		prop: 'estimatedArrivalTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '实际送达时间',
		prop: 'actualArrivalTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '状态',
		prop: 'status',
	},
	{
		label: '创建时间',
		prop: 'createTime',
		width: 150,
		formatter: X_DATE_UTILS.formatDate,
	},
	{
		label: '操作',
		prop: 'action',
		width: 180,
	},
]
const orderModalVisible = ref(false)
const orderDetail = ref(null)
const orderDetailLoading = ref(false)

async function handleView(row) {
	orderModalVisible.value = true
	orderDetail.value = row
}
</script>
<template>
	<BizLayoutPageContentContainer :plain="containerPlain">
		<BizFormModalsJnrmTransportOrder ref="modalRef" @success="run()" />
		<BizCardsQuery v-if="searchable">
			<BizFormQueryForm :model="queryModel">
				<XFormItem v-if="userStore.isPlatformSuper" label="所属电厂" prop="deptId">
					<XSelect
						v-model="queryModel.deptId"
						:clearable="false"
						:remote-method="BIZ_Dept_APIS.getPage"
						placeholder="请输入搜索"
						@keyup.enter="run"
					/>
				</XFormItem>
				<XFormItem label="运单号" prop="orderNo">
					<XInput v-model="queryModel.orderNo" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem v-if="!computedParams.licensePlate" label="车牌号" prop="licensePlate">
					<XInput v-model="queryModel.licensePlate" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="司机姓名" prop="driverName">
					<XInput v-model="queryModel.driverName" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="司机手机号" prop="driverPhone">
					<XInput v-model="queryModel.driverPhone" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="状态" prop="status">
					<XSelect v-model="queryModel.status" placeholder="请选择搜索" tag-group-name="JNRM运单状态" :clearable="false" />
				</XFormItem>
				<XFormItem label="货物信息" prop="cargoInfo">
					<XInput v-model="queryModel.cargoInfo" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<XFormItem label="开始时间" prop="startTime">
					<XDateTimePicker
						v-model="queryModel.startTime"
						:clearable="false"
						range
						show-shortcuts
						type="datetime"
						placeholder="请输入搜索"
						@keyup.enter="run"
					/>
				</XFormItem>
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div></div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="addable && hasPermission('x:jnrm-transport-order:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #departureName="{ row }">
					<div>
						<div>{{ row.departureName }}</div>
						->
						<div>{{ row.destinationName }}</div>
					</div>
				</template>
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<x-button text-mode @click="handleView(row)">查看</x-button>
						<BizButtonsUpdate v-if="hasPermission('x:jnrm-transport-order:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:jnrm-transport-order:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
		<x-modal v-model="orderModalVisible" title="运单详情" container-class="!w-auto !min-w-50vw">
			<div v-loading="orderDetailLoading" class="min-h-96">
				<div v-if="orderDetail" class="space-y-base">
					<!-- 订单基本信息 -->
					<div class="rounded-lg bg-gray-50 p-base">
						<h3 class="text-gray-800 font-medium text-lg mb-base">订单基本信息</h3>
						<div class="grid grid-cols-1 gap-base lg:grid-cols-3 md:grid-cols-2">
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">运单号</label>
								<div class="text-gray-900 text-base">{{ orderDetail.orderNo }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">出发地</label>
								<div class="text-gray-900 text-base">{{ orderDetail.departureName }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">目的地</label>
								<div class="text-gray-900 text-base">{{ orderDetail.destinationName }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">车牌号</label>
								<div class="text-gray-900 text-base">{{ orderDetail.licensePlate }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">司机姓名</label>
								<div class="text-gray-900 text-base">{{ orderDetail.driverName }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">司机手机号</label>
								<div class="text-gray-900 text-base">{{ orderDetail.driverPhone }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">货物信息</label>
								<div class="text-gray-900 text-base">{{ orderDetail.cargoInfo }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">货物重量</label>
								<div class="text-gray-900 text-base">{{ orderDetail.cargoWeight }} 吨</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">状态</label>
								<div class="text-base">
									<!--									<XTag :tag-group-name="'JNRM运单状态'" :value="orderDetail.status" />-->
								</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">开始时间</label>
								<div class="text-gray-900 text-base">{{ X_DATE_UTILS.formatDate(orderDetail.startTime) }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">预估送达时间</label>
								<div class="text-gray-900 text-base">{{ X_DATE_UTILS.formatDate(orderDetail.estimatedArrivalTime) }}</div>
							</div>
							<div class="space-y-xs">
								<label class="text-gray-600 font-medium text-sm">实际送达时间</label>
								<div class="text-gray-900 text-base">{{ X_DATE_UTILS.formatDate(orderDetail.actualArrivalTime) }}</div>
							</div>
						</div>
					</div>

					<!-- 列表区域 - 预留给您后续添加 -->
					<div class="border rounded-lg bg-white mt-base p-base">
						<h3 class="text-gray-800 font-medium text-lg">告警信息</h3>
						<!-- 这里您可以添加相关的列表内容，比如运输轨迹、GPS记录等 -->
						<jnrm-alert container-plain :searchable="false" :params="{ transportOrderId: orderDetail.id }" />
					</div>
				</div>
			</div>
		</x-modal>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
