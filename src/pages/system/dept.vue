<route lang="json">
{
	"meta": {
		"title": ""
	}
}
</route>
<script setup lang="ts">
const props = defineProps({
	params: {
		type: Object,
		default: () => ({}),
	},
})
const computedParams = computed(() => props.params)
const queryModel = reactive({
	pageNo: 1,
	pageSize: 10,
	name: null,
	parentId: null,
	sort: null,
	leaderUserId: null,
	phone: null,
	email: null,
	status: null,
	createTime: [],
	entityId: null,
	type: null,
	code: null,
	address: null,
	contactPerson: null,
	longitude: null,
	latitude: null,
	...computedParams.value,
})

const modalRef = ref(null)
const BizTablesListRef = ref()
const { loading, run, list, total, reset } = xUsePageRequest(BIZ_Dept_APIS.getPage, queryModel)
onMounted(() => {
	run()
})

async function handleDelete(id) {
	await toast.confirm('确定删除吗？')
	loading.value = true
	await BIZ_Dept_APIS.delete(id).finally(() => {
		loading.value = false
	})
	toast.success('删除成功')
	if (!BizTablesListRef.value?.getTableRef()?.deleteNode({ id })) {
		reset()
	}
}

function handleOpenModal(type, id) {
	modalRef.value.open(type, id, computedParams.value)
}

const columns = [
	{
		label: '类型',
		prop: 'type',
	},
	{
		label: '名称',
		prop: 'name',
	},

	{
		label: '位置',
		prop: 'locationName',
	},
	{
		label: '联系电话',
		prop: 'phone',
	},
	{
		label: '操作',
		prop: 'action',
	},
]
</script>
<template>
	<BizLayoutPageContentContainer>
		<BizFormModalsDept ref="modalRef" @success="run()" />
		<BizCardsQuery>
			<BizFormQueryForm :model="queryModel">
				<XFormItem label="名称" prop="name">
					<XInput v-model="queryModel.name" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<!--				<XFormItem label="联系人" prop="contactPerson">-->
				<!--					<XInput v-model="queryModel.contactPerson" placeholder="请输入搜索" @keyup.enter="run" />-->
				<!--				</XFormItem>-->
				<XFormItem label="联系电话" prop="phone">
					<XInput v-model="queryModel.phone" placeholder="请输入搜索" @keyup.enter="run" />
				</XFormItem>
				<!--				<XFormItem label="编码" prop="code">-->
				<!--					<XInput v-model="queryModel.code" placeholder="请输入搜索" @keyup.enter="run" />-->
				<!--				</XFormItem>-->
				<!--				<XFormItem label="地址" prop="address">-->
				<!--					<XInput v-model="queryModel.address" placeholder="请输入搜索" @keyup.enter="run" />-->
				<!--				</XFormItem>-->
			</BizFormQueryForm>
			<div class="flex items-center justify-end gap-xs">
				<BizButtonsReset @click="reset" />
				<BizButtonsQuery @click="run" />
			</div>
		</BizCardsQuery>
		<div class="flex items-center justify-between py-xs">
			<div>
				<XRadioGroup v-model="queryModel.type" placeholder="请选择搜索" tag-group-name="ADMIN部门类型" @change="run" />
			</div>
			<div class="flex items-center">
				<BizButtonsCreate v-if="hasPermission('x:dept:create')" @click="handleOpenModal('create')" />
			</div>
		</div>
		<BizCardsTable v-loading="loading">
			<BizTablesList ref="BizTablesListRef" :data="list" :columns="columns">
				<template #action="{ row, rowIndex }">
					<div class="flex items-center justify-center gap-xs">
						<BizButtonsUpdate v-if="hasPermission('x:dept:update')" @click="handleOpenModal('update', row.id)" />
						<BizButtonsDelete v-if="hasPermission('x:dept:delete')" @click="handleDelete(row.id)" />
					</div>
				</template>
			</BizTablesList>
			<div class="flex justify-end mt-base">
				<XPagination v-model="queryModel.pageNo" v-model:page-size="queryModel.pageSize" :total="total" @change="run" />
			</div>
		</BizCardsTable>
	</BizLayoutPageContentContainer>
</template>
<style scoped></style>
